// Module feature descriptions based on Gita Connect requirements

export const moduleFeatures = {
  alumniDirectory: {
    title: "Gita Connect Directory",
    description: "Browse and connect with Gita family members across different domains for professional networking and support",
    features: [
      "Search and filter alumni by industry, graduation year, and expertise",
      "View detailed profiles with professional background and skills",
      "Connect with mentors available for guidance and support",
      "Direct messaging capabilities for one-on-one communication",
      "Network across multiple domains including Healthcare, Engineering, Arts & Crafts",
      "Find alumni by location for local networking opportunities",
      "Identify members offering specific types of help or seeking support",
      "Track mentor availability status (available, busy, unavailable)"
    ]
  },
  
  alumniOpportunities: {
    title: "Gita Connect Opportunities",
    description: "Access internships, jobs, accommodations, and domain-specific support from the Gita family network",
    features: [
      "Browse job postings and internship opportunities from alumni companies",
      "Find accommodation support for students and professionals",
      "Access domain-specific opportunities in Healthcare, Engineering, Arts, and more",
      "Filter opportunities by location, experience level, and industry",
      "Submit interest and connect directly with opportunity providers",
      "Track application status and manage your submissions",
      "Set preferences for personalized opportunity recommendations",
      "Receive notifications for new matching opportunities"
    ]
  },
  
  alumniProfile: {
    title: "Gita Family Member Profile",
    description: "View detailed profile, expertise, and connect for mentorship or professional collaboration",
    features: [
      "Comprehensive professional background and education details",
      "Skills and expertise showcase with endorsements",
      "Mentorship availability and areas of guidance",
      "Professional achievements and certifications",
      "Contact information and preferred communication methods",
      "Activity history and community contributions",
      "Connection status and mutual connections",
      "Direct messaging and mentorship request options"
    ]
  },
  
  memberDashboard: {
    title: "Member Dashboard",
    description: "Access your personalized workspace to offer help, seek support, and connect with the Gita family",
    features: [
      "Personalized feed based on your preferences and domains",
      "Quick access to offer help or seek support functions",
      "Real-time notifications for relevant opportunities and requests",
      "Track your active postings and their engagement metrics",
      "View responses to your help requests and offers",
      "Manage your chat conversations and connections",
      "Monitor your community engagement and impact score",
      "Access shortcuts to frequently used features",
      "Upcoming events and community announcements"
    ]
  },
  
  browsePostings: {
    title: "Browse Postings",
    description: "Explore help requests and support offerings from Gita family members across various domains",
    features: [
      "Filter postings by category (Offer Help vs Seek Help)",
      "Search by domain: Healthcare, Engineering, Arts, Music, Real Estate",
      "Advanced filtering by urgency level, date posted, and location",
      "Express interest in postings with one-click actions",
      "View posting details including contact information",
      "Track posting expiration dates (30-day automatic expiry)",
      "Save postings for later reference",
      "Share relevant postings with other members",
      "Report inappropriate or duplicate postings"
    ]
  },
  
  myPostings: {
    title: "My Postings",
    description: "Manage your help offerings and support requests within the Gita family network",
    features: [
      "Create new postings for offering help or seeking support",
      "Manage active postings with edit and delete capabilities",
      "Track engagement metrics (views, interests, responses)",
      "Monitor posting status (pending, approved, rejected)",
      "View moderator feedback on rejected postings",
      "Extend posting expiration dates when needed",
      "Manage responses and connect with interested members",
      "Archive completed postings for future reference",
      "Analytics on posting performance and success rate"
    ]
  },
  
  mentorshipPlatform: {
    title: "Gita Connect Mentorship",
    description: "Connect with experienced mentors for guidance in education, career, and professional development",
    features: [
      "Browse mentors by expertise area and availability",
      "Request mentorship in specific domains or skills",
      "Schedule one-on-one mentoring sessions",
      "Access group mentoring programs and workshops",
      "Track mentorship progress and goals",
      "Rate and review mentorship experiences",
      "Join study groups for collaborative learning",
      "Access mentorship resources and materials",
      "Participate in mentor-led webinars and Q&A sessions"
    ]
  },
  
  analyticsDashboard: {
    title: "Gita Connect Analytics",
    description: "Monitor user activity, success metrics, and platform performance insights",
    features: [
      "Track total postings by category and time period",
      "Monitor user engagement metrics and activity trends",
      "View success rates for help requests and offers",
      "Analyze popular categories and domains",
      "Track member growth and retention rates",
      "Monitor response times and quality scores",
      "Generate reports for moderator review",
      "Identify trending topics and emerging needs",
      "Export analytics data for external analysis"
    ]
  }
}