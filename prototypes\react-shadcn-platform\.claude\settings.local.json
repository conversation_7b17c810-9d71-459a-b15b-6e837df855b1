{"permissions": {"allow": ["Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "WebSearch", "WebFetch(domain:github.com)", "WebFetch(domain:tanstack.com)", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm install:*)", "Bash(npm run type-check:*)", "Bash(npx:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(npm run check:all:*)", "Bash(npm run validate:theme:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npm run hook:validate:*)", "Bash(npm run:*)", "Bash(git checkout:*)", "Bash(netsh advfirewall firewall show rule:*)", "<PERSON><PERSON>(netsh:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(gh:*)", "<PERSON><PERSON>(winget:*)", "Bash(set GITHUB_TOKEN)", "Bash(cp:*)", "Bash(grep:*)", "Bash(npm audit fix:*)", "Bash(find:*)", "Bash(xcopy:*)"], "deny": [], "ask": [], "additionalDirectories": ["C:\\React-Projects\\SGSGitaMahayagna\\public\\images", "C:\\React-Projects\\SGSGitaMahayagna\\src\\assets\\images"]}, "hooks": {"UserPromptSubmit": [{"hooks": [{"type": "command", "command": "python .claude/hooks/user_prompt_submit.py"}]}], "PreToolUse": [{"hooks": [{"type": "command", "command": "python .claude/hooks/pre_tool_use.py"}]}], "PostToolUse": [{"hooks": [{"type": "command", "command": "python .claude/hooks/post_tool_use.py"}]}], "SessionStart": [{"hooks": [{"type": "command", "command": "python .claude/hooks/session_start.py"}]}], "Stop": [{"hooks": [{"type": "command", "command": "python .claude/hooks/stop.py"}]}], "SubagentStop": [{"hooks": [{"type": "command", "command": "python .claude/hooks/subagent_stop.py"}]}]}}